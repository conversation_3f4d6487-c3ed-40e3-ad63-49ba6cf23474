'use client';

import React, { useCallback, useMemo } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import {
  Instagram,
  Facebook,
  CalendarCheck,
  MessageCircle
} from 'lucide-react';

import { blogPosts } from '@/data/blogPosts';
import TestimonialSlider from '@/components/TestimonialSlider';

// Enhanced SafeIcon component with better prop types and accessibility
const SafeIcon = React.memo(({ Icon, className = '', size = 'default', ...props }) => {
  if (!Icon) return null;
  
  const sizeClasses = {
    small: 'w-4 h-4',
    default: 'w-5 h-5',
    large: 'w-6 h-6',
    xl: 'w-8 h-8'
  };
  
  return (
    <Icon 
      className={`${sizeClasses[size]} ${className}`} 
      {...props}
      role="img"
      aria-hidden="true"
    />
  );
});
SafeIcon.displayName = 'SafeIcon';



// Ultra-Minimalist Hero Section - Editorial Style
const HeroSection = React.memo(() => {
  const scrollToRetreatSection = useCallback(() => {
    const element = document.getElementById('retreats');
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      });
    }
  }, []);

  return (
    <section className="relative h-screen flex items-center justify-center overflow-hidden">
      {/* Clean Background Image */}
      <div className="absolute inset-0 w-full h-full">
        <Image
          src="/images/background/bali-hero.webp"
          alt="Bali retreat sanctuary"
          fill
          priority
          className="object-cover"
          sizes="100vw"
          quality={95}
        />
      </div>

      {/* Hero Content - Ultra Clean */}
      <div className="relative z-10 text-center px-6 max-w-4xl mx-auto">
        {/* Main Title */}
        <h1 className="text-6xl md:text-7xl font-serif font-light mb-6 text-white tracking-tight leading-tight">
          BAKASANA
        </h1>

        {/* Subtitle */}
        <p className="text-xl md:text-2xl text-white font-light mb-12 tracking-wide">
          Transformative Wellness Retreats
        </p>

        {/* Simple Ghost Button */}
        <button
          onClick={scrollToRetreatSection}
          className="px-8 py-4 border border-white text-white font-light tracking-wide uppercase text-sm hover:opacity-70 transition-opacity duration-200"
        >
          Discover Retreats
        </button>
      </div>
    </section>
  );
});
HeroSection.displayName = 'HeroSection';

// Ultra-Minimalist Card Component - Editorial Style
const PremiumCard = React.memo(({
  title,
  description,
  link,
  imageUrl,
  price,
  location,
  duration,
  className = ''
}) => {
  return (
    <article className={`${className}`}>
      {/* Sharp-edged Image */}
      {imageUrl && (
        <div className="relative aspect-[4/3] overflow-hidden mb-6">
          <Image
            src={imageUrl}
            alt={title}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            quality={90}
          />
        </div>
      )}

      {/* Content - Typography Only */}
      <div className="space-y-4">
        <div className="flex items-start justify-between">
          <h3 className="text-2xl font-serif font-light text-black leading-tight">
            {link ? (
              <Link
                href={link}
                className="hover:opacity-70 transition-opacity duration-200"
              >
                {title}
              </Link>
            ) : (
              title
            )}
          </h3>

          {price && (
            <div className="text-right">
              <div className="text-lg font-light text-charcoal">{price}</div>
            </div>
          )}
        </div>

        {location && (
          <p className="text-sm font-light text-gray-600 uppercase tracking-wide">
            {location}
          </p>
        )}

        <p className="text-gray-600 leading-relaxed font-light">
          {description}
        </p>
      </div>
    </article>
  );
});
PremiumCard.displayName = 'PremiumCard';

// Ultra-Minimal Section Divider
const SectionDivider = React.memo(() => {
  return (
    <div className="section-divider my-24"></div>
  );
});
SectionDivider.displayName = 'SectionDivider';





// Main WellnessPage Component - Enhanced Enterprise Version
const WellnessPage = ({ latestPosts }) => {
  const router = useRouter();
  const posts = useMemo(() => latestPosts || blogPosts.slice(0, 3), [latestPosts]);

  // Enhanced retreat highlights with more detailed information
  const retreatHighlights = useMemo(() => [
    {
      id: 'ubud',
      title: 'Sanktuarium Ubud',
      description: 'Siedem dni duchowego zanurzenia w kulturowym sercu Bali. Odkryj swoją wewnętrzną mądrość wśród starożytnych tarasów ryżowych i świętych świątyń.',
      imageUrl: '/images/retreats/ubud.webp',
      location: 'Ubud, Bali',
      duration: '7 dni',
      price: '€2,400',
      badge: 'Popularne',
      highlights: [
        'Codzienna joga i medytacja',
        'Wycieczki do świętych świątyń',
        'Warsztaty z filozofii jogi',
        'Kąpiele w górskich źródłach',
        'Certyfikowane zakwaterowanie'
      ]
    },
    {
      id: 'gili-air',
      title: 'Raj Gili Air',
      description: 'Pięć dni czystego spokoju na rajskiej wyspie, gdzie czas płynie wolno, a pokój przychodzi naturalnie. Odnowa w towarzystwie inspirujących kobiet.',
      imageUrl: '/images/retreats/gili.webp',
      location: 'Gili Air, Indonezja',
      duration: '5 dni',
      price: '€1,800',
      badge: 'Ekskluzywne',
      highlights: [
        'Plaża prywatna tylko dla grupy',
        'Snorkeling z żółwiami',
        'Yoga na wschodzie słońca',
        'Masaże balijskie',
        'Maksymalnie 6 osób'
      ]
    },
    {
      id: 'canggu',
      title: 'Klify Canggu',
      description: 'Dziesięć dni transformacji z sesjami jogi na klifach z widokiem na nieskończony ocean. Spektakularne zachody słońca i wewnętrzna przemiana.',
      imageUrl: '/images/retreats/canggu.webp',
      location: 'Canggu, Bali',
      duration: '10 dni',
      price: '€3,200',
      badge: 'Intensywne',
      highlights: [
        'Joga na klifach o zachodzie',
        'Warsztaty transformacji',
        'Surfing dla początkujących',
        'Ceremonie pełni księżyca',
        'Indywidualne sesje coaching'
      ]
    },
    {
      id: 'sri-lanka',
      title: 'Perła Sri Lanka',
      description: 'Nowy wymiar duchowości w perle Oceanu Indyjskiego. Odkryj starożytną mądrość Ajurwedy i buddyjskich praktyk w otoczeniu dziewiczej przyrody.',
      imageUrl: '/images/retreats/sri-lanka.webp',
      location: 'Ella, Sri Lanka',
      duration: '8 dni',
      price: '€2,800',
      badge: 'Nowość',
      highlights: [
        'Authentic Ayurvedic treatments',
        'Buddhist meditation sessions',
        'Tea plantation visits',
        'Elephant sanctuary experience',
        'Traditional Sri Lankan cuisine'
      ]
    }
  ], []);

  // Enhanced testimonials with more details
  const testimonials = useMemo(() => [
    {
      quote: "To była najbardziej transformująca podróż mojego życia. Julia stworzyła przestrzeń ciepła i bezpieczeństwa, która pozwoliła mi prawdziwie połączyć się z sobą. Wróciłam jako zupełnie nowa kobieta, pełna pewności siebie i wewnętrznego spokoju.",
      author: "Anna Kowalska",
      location: "Warszawa",
      rating: 5,
      retreat: "Sanktuarium Ubud",
      avatar: "/images/testimonials/anna.webp"
    },
    {
      quote: "Idealny balans jogi, kultury i relaksu. Julia ma dar tworzenia magicznych chwil, które zostają w pamięci na zawsze. Nasza grupa stała się jak druga rodzina - kobiety wspierające się nawzajem w drodze do siebie.",
      author: "Katarzyna Nowak",
      location: "Gdańsk",
      rating: 5,
      retreat: "Raj Gili Air",
      avatar: "/images/testimonials/kasia.webp"
    },
    {
      quote: "Każdy dzień przynosił nowe odkrycia i głębsze zrozumienie siebie. Julia prowadzi z taką mądrością i empatią, że czujesz się bezpiecznie na każdym kroku. To nie był tylko retreat - to była prawdziwa podróż do siebie.",
      author: "Marta Wiśniewska",
      location: "Wrocław",
      rating: 5,
      retreat: "Klify Canggu",
      avatar: "/images/testimonials/marta.webp"
    },
    {
      quote: "Bali z Julią to nie tylko joga - to odkrywanie swojej wewnętrznej siły i potencjału. Wróciłam z nową energią, spokojem wewnętrznym i pewnością, że mogę wszystko. Polecam każdej kobiecie, która szuka prawdziwej zmiany.",
      author: "Agnieszka Zielińska",
      location: "Kraków",
      rating: 5,
      retreat: "Sanktuarium Ubud",
      avatar: "/images/testimonials/agnieszka.webp"
    }
  ], []);

  // Enhanced FAQs with more comprehensive answers
  const faqs = useMemo(() => [
    {
      question: "Czy retreaty są odpowiednie dla początkujących w jodze?",
      answer: "Absolutnie tak! Nasze retreaty przyjmują kobiety na każdym poziomie doświadczenia. Julia prowadzi każdą sesję z uwagą na indywidualne potrzeby i możliwości. Oferujemy modyfikacje dla każdego ćwiczenia, więc każda znajdzie swoje miejsce w naszym kręgu, niezależnie od poziomu zaawansowania."
    },
    {
      question: "Co dokładnie jest wliczone w cenę retreatu?",
      answer: "Cena obejmuje pełne zakwaterowanie w starannie wybranych miejscach, wszystkie wegetariańskie/wegańskie posiłki przygotowane z lokalnych składników, codzienne sesje jogi i medytacji, wycieczki kulturalne z przewodnikiem, transfery lotniskowe, ubezpieczenie grupowe oraz wsparcie Julii przez całą podróż. Jedyne dodatkowe koszty to loty i ewentualne zakupy osobiste."
    },
    {
      question: "Jakie są terminy najbliższych retreatów?",
      answer: "Nasze nadchodzące retreaty zaplanowane są na: czerwiec 2024 (Ubud), lipiec 2024 (Gili Air), wrzesień 2024 (Canggu) oraz październik 2024 (Sri Lanka - nowość!). Szczegółowe daty i dostępność miejsc znajdziesz w sekcji kalendarza. Rezerwacja miejsc odbywa się z wyprzedzeniem 3-6 miesięcy."
    },
    {
      question: "Czy muszę mieć wcześniejsze doświadczenie w medytacji?",
      answer: "Nie, żadne wcześniejsze doświadczenie nie jest konieczne. Nasze retreaty są idealne zarówno dla osób rozpoczynających swoją duchową podróż, jak i dla bardziej zaawansowanych praktyków. Julia wprowadza techniki medytacji stopniowo, zawsze dostosowując je do grupy i indywidualnych potrzeb każdej uczestniczki."
    },
    {
      question: "Jak duże są grupy na retreatach?",
      answer: "Nasze grupy są świadomie małe i intymne - zazwyczaj 6-12 uczestniczek, maksymalnie 15 osób. To zapewnia indywidualną uwagę Julii, możliwość nawiązania głębokich, znaczących połączeń z innymi kobietami oraz stworzenie bezpiecznej przestrzeni dla osobistych przemian."
    },
    {
      question: "Jakie są warunki pogodowe i co spakować?",
      answer: "Bali i Sri Lanka cieszą się tropikalnym klimatem przez cały rok. Temperatura wynosi 26-32°C, z możliwością opadów (szczególnie październik-marzec). Przygotowujemy szczegółową listę rzeczy do spakowania dla każdej uczestniczki, uwzględniającą specyfikę konkretnego retreatu i pory roku."
    }
  ], []);

  // Enhanced retreat schedule with more details
  const retreats = useMemo(() => [
    {
      id: 'ubud-june-2024',
      type: 'Transformacja Życia',
      title: 'Sanktuarium Ubud',
      startDate: '15 czerwca 2024',
      endDate: '22 czerwca 2024',
      location: 'Ubud, Bali',
      participants: 8,
      maxParticipants: 12,
      price: '€2,400',
      originalPrice: '€2,800',
      description: 'Odkryj swoją wewnętrzną siłę w duchowym sercu Bali. Julia osobiście prowadzi każdą sesję w otoczeniu starożytnych świątyń i tarasów ryżowych.',
      available: true,
      status: 'early-bird',
      highlights: ['Codzienna joga i medytacja', 'Wycieczki do świątyń', 'Warsztaty filozofii jogi'],
      accommodation: '4* eco-resort',
      meals: 'Wegetariańskie/wegańskie',
      activities: ['Wycieczki do świątyń', 'Spacery po tarasach ryżowych', 'Tradycyjne targi', 'Terapie uzdrawiające']
    },
    {
      id: 'gili-air-july-2024',
      type: 'Odnowa Duszy',
      title: 'Raj Gili Air',
      startDate: '20 lipca 2024',
      endDate: '25 lipca 2024',
      location: 'Gili Air, Indonezja',
      participants: 5,
      maxParticipants: 8,
      price: '€1,800',
      description: 'Pięć dni czystego spokoju na rajskiej wyspie. Idealne miejsce na reset i głęboką odnowę w małej, intymnej grupie.',
      available: true,
      status: 'filling-fast',
      highlights: ['Snorkeling z żółwiami', 'Yoga na plaży', 'Masaże balijskie'],
      accommodation: 'Beachfront bungalows',
      meals: 'Fresh seafood & vegetarian',
      activities: ['Snorkeling z koralowcami', 'Joga o zachodzie słońca', 'Wycieczki rowerowe', 'Medytacja na plaży']
    },
    {
      id: 'canggu-september-2024',
      type: 'Intensywna Przemiana',
      title: 'Klify Canggu',
      startDate: '10 września 2024',
      endDate: '20 września 2024',
      location: 'Canggu, Bali',
      participants: 10,
      maxParticipants: 15,
      price: '€3,200',
      description: 'Dziesięć dni głębokiej transformacji z jogą na klifach i spektakularnymi zachodami słońca nad oceanem.',
      available: true,
      status: 'confirmed',
      highlights: ['Joga na klifach', 'Surfing lessons', 'Ceremonie księżyca'],
      accommodation: 'Luxury cliff-top villa',
      meals: 'Gourmet vegetarian cuisine',
      activities: ['Joga na klifach', 'Nauka surfingu', 'Wizyty przy wodospadach', 'Święte ceremonie']
    },
    {
      id: 'sri-lanka-october-2024',
      type: 'Ayurveda & Mindfulness',
      title: 'Perła Sri Lanka',
      startDate: '15 października 2024',
      endDate: '23 października 2024',
      location: 'Ella, Sri Lanka',
      participants: 0,
      maxParticipants: 10,
      price: '€2,800',
      description: 'Nowy wymiar duchowości w perle Oceanu Indyjskiego. Odkryj starożytną mądrość Ajurwedy i buddyjskich praktyk.',
      available: true,
      status: 'new',
      highlights: ['Authentic Ayurveda', 'Buddhist meditation', 'Tea plantation visits'],
      accommodation: 'Boutique mountain resort',
      meals: 'Traditional Sri Lankan & Ayurvedic',
      activities: ['Terapie ajurwedyjskie', 'Wizyty w świątyniach', 'Sanktuarium słoni', 'Podróże koleją']
    }
  ], []);

  // Enhanced social links with more platforms
  const socialLinks = useMemo(() => [
    { 
      id: 'instagram', 
      href: 'https://www.instagram.com/fly_with_bakasana', 
      label: 'Instagram', 
      icon: Instagram,
      description: 'Codzienne inspiracje i zdjęcia z naszych podróży'
    },
    { 
      id: 'facebook', 
      href: 'https://www.facebook.com/p/Fly-with-bakasana-100077568306563/', 
      label: 'Facebook', 
      icon: Facebook,
      description: 'Dołącz do naszej społeczności na Facebooku'
    },
    { 
      id: 'bookings', 
      href: 'https://app.fitssey.com/Flywithbakasana/frontoffice', 
      label: 'Rezerwacje', 
      icon: CalendarCheck,
      description: 'Zarezerwuj swoje miejsce na retreatach'
    },
    {
      id: 'whatsapp',
      href: 'https://wa.me/48123456789',
      label: 'WhatsApp',
      icon: MessageCircle,
      description: 'Bezpośredni kontakt z Julią'
    }
  ], []);

  return (
    <div className="wellness-page bg-white">
      <HeroSection />

      {/* Ultra-Clean Retreat Section */}
      <section id="retreats" className="container">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-serif font-light mb-8 text-black">
            Our Retreats
          </h2>
          <p className="text-lg text-gray-600 font-light max-w-3xl mx-auto leading-relaxed">
            Transformative wellness experiences in the most sacred places of Asia.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-16">
          {retreatHighlights.map((retreat) => (
            <PremiumCard
              key={retreat.id}
              title={retreat.title}
              description={retreat.description}
              imageUrl={retreat.imageUrl}
              link={`/program/${retreat.id}`}
              location={retreat.location}
              duration={retreat.duration}
              price={retreat.price}
            />
          ))}
        </div>
      </section>

      <SectionDivider />

      {/* About Julia - Ultra Clean */}
      <section className="container">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-start">
          <div className="aspect-[4/5] relative overflow-hidden">
            <Image
              src="/images/profile/omnie-opt.webp"
              alt="Julia Jakubowicz - Certified Yoga Instructor"
              fill
              className="object-cover"
              sizes="(max-width: 1024px) 100vw, 50vw"
              quality={95}
            />
          </div>

          <div className="space-y-8">
            <h2 className="text-4xl font-serif font-light text-black">About Julia</h2>

            <p className="text-lg text-gray-600 font-light leading-relaxed">
              "True practice begins when we step off the mat. It's there, in daily life,
              that we discover our real strength and wisdom that has been within us all along."
            </p>

            <p className="text-gray-600 font-light leading-relaxed">
              Certified yoga instructor with 8 years of experience and a passion for sharing
              the wisdom of ancient practices. Julia completed her studies at a prestigious
              yoga school in Rishikesh, India, and deepens her knowledge during regular stays in Asia.
            </p>

            <p className="text-gray-600 font-light leading-relaxed">
              She specializes in Hatha and Vinyasa Flow, meditation practices, and yoga philosophy.
              Bali and Sri Lanka have become her second spiritual homes, places where she guides
              women on their transformational journeys to themselves.
            </p>
          </div>
        </div>
      </section>

      <SectionDivider />

      {/* Testimonials - Ultra Clean */}
      <section className="container">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-serif font-light mb-8 text-black">
            Client Stories
          </h2>
        </div>

        <TestimonialSlider testimonials={testimonials} />
      </section>

      <SectionDivider />

      {/* Contact - Ultra Clean */}
      <section className="container">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-serif font-light mb-8 text-black">
            Contact
          </h2>
          <p className="text-lg text-gray-600 font-light max-w-2xl mx-auto leading-relaxed">
            Ready for transformation? Have questions? Contact us.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-16">
          {socialLinks.map((link) => (
            <a
              key={link.id}
              href={link.href}
              target="_blank"
              rel="noopener noreferrer"
              className="text-center p-6 hover:opacity-70 transition-opacity duration-200"
            >
              <div className="mb-4">
                <SafeIcon Icon={link.icon} className="w-6 h-6 text-charcoal mx-auto" />
              </div>
              <h3 className="font-medium text-gray-800 mb-2">{link.label}</h3>
              <p className="text-sm text-gray-600 font-light">{link.description}</p>
            </a>
          ))}
        </div>
      </section>

      <SectionDivider />

      {/* Blog - Ultra Clean */}
      <section className="container">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-serif font-light mb-8 text-black">
            Journal
          </h2>
          <p className="text-lg text-gray-600 font-light max-w-2xl mx-auto leading-relaxed">
            Inspiring stories and insights from our transformational journeys.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-16">
          {posts.map((post) => (
            <PremiumCard
              key={post.id}
              title={post.title}
              description={post.excerpt}
              link={`/blog/${post.slug}`}
              imageUrl={post.image}
            />
          ))}
        </div>

        <div className="text-center mt-16">
          <Link
            href="/blog"
            className="btn"
          >
            View All Articles
          </Link>
        </div>
      </section>
    </div>
  );
};

export default WellnessPage;