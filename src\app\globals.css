@tailwind base;
@tailwind components;
@tailwind utilities;

/* ULTRA-MINIMALIST PROFESSIONAL GLOBALS */

:root {
  --black: #000000;
  --charcoal: #2C2C2C;
  --gray: #666666;
  --light-gray: #E5E5E5;
  --white: #FFFFFF;
  --warm-white: #FAFAF8;
}

/* GLOBAL RULES FOR ULTRA-MINIMALISM */
* {
  box-sizing: border-box;
  border-radius: 0 !important; /* Zero rounded corners globally */
}

html {
  scroll-behavior: smooth;
  -webkit-font-smoothing: antialiased;
}

body {
  font-family: 'Helvetica Neue', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 300;
  line-height: 1.8;
  color: var(--charcoal);
  background: var(--warm-white);
  overflow-x: hidden;
}

/* TYPOGRAPHY HIERARCHY */
h1, h2, h3 {
  font-family: 'Didot', 'Bodoni MT', 'Playfair Display', serif;
  font-weight: 400;
  letter-spacing: 0.02em;
  line-height: 1.2;
  color: var(--black);
}

h1 {
  font-size: 4rem;
  letter-spacing: 0.2em;
}

h2 {
  font-size: 2.5rem;
}

h3 {
  font-size: 1.5rem;
}

p {
  margin-bottom: 1.5rem;
  font-weight: 300;
}

a {
  color: inherit;
  text-decoration: none;
  transition: opacity 0.2s ease;
}

a:hover {
  opacity: 0.7; /* Only opacity changes on hover */
}

img {
  max-width: 100%;
  height: auto;
  display: block;
  width: 100%;
}

/* PROFESSIONAL LAYOUT */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 120px 10%; /* Generous breathing space */
}

.section {
  padding: 120px 0;
}

/* GHOST BUTTONS - ULTRA MINIMAL */
.btn {
  padding: 15px 40px;
  border: 1px solid currentColor;
  background: transparent;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-size: 12px;
  font-family: 'Helvetica Neue', sans-serif;
  font-weight: 300;
  transition: opacity 0.2s ease;
  cursor: pointer;
  display: inline-block;
}

.btn:hover {
  opacity: 0.7;
}

/* SECTION DIVIDERS - MINIMAL */
.section-divider {
  width: 60px;
  height: 1px;
  background: var(--light-gray);
  margin: 0 auto;
}

/* UTILITIES */
.text-center { text-align: center; }

/* REMOVE ALL ANIMATIONS, SHADOWS, GRADIENTS */
.animate-pulse,
.animate-bounce,
.animate-spin,
.shadow,
.shadow-sm,
.shadow-md,
.shadow-lg,
.shadow-xl,
.backdrop-blur,
.backdrop-blur-sm,
.backdrop-blur-md,
.bg-gradient-to-r,
.bg-gradient-to-b,
.bg-gradient-to-t,
.bg-gradient-to-l {
  animation: none !important;
  box-shadow: none !important;
  backdrop-filter: none !important;
  background: transparent !important;
}

/* MINIMAL SCROLLBAR */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--sage);
  opacity: 0.3;
}